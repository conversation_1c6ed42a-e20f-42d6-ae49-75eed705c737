import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css'

// Import accessibility enhancements (auto-initializes in development)
import './utils/accessibilityEnhancer';
import './styles/no-scrollbar-arrows.css';  // إزالة أسهم شريط التمرير أولاً
import './styles/scrollbar.css';            // ثم تطبيق التخصيص
import './styles/fullscreen-fixes.css';     // إصلاحات وضع ملء الشاشة
import './styles/emoji.css';
import { applyCSP } from './utils/csp';

// Import scrollbar utilities (auto-initializes)
import './utils/scrollbarUtils';

// Import fullscreen dark mode service (auto-initializes)
import './services/FullscreenDarkModeService';

// تطبيق CSP بناءً على البيئة
applyCSP();

// تعطيل StrictMode لتجنب التحميل المتكرر في بيئة التطوير
// StrictMode يسبب تشغيل useEffect مرتين في بيئة التطوير
ReactDOM.createRoot(document.getElementById('root')!).render(
  <App />
);