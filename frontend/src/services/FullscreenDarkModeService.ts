/**
 * 🖥️ خدمة إدارة وضع ملء الشاشة والوضع المظلم - SmartPOS Fullscreen Dark Mode Service
 * 
 * خدمة شاملة لحل مشاكل شريط التمرير في وضع ملء الشاشة
 * تضمن عمل الوضع المظلم بشكل صحيح في جميع الحالات
 */

export class FullscreenDarkModeService {
  private static instance: FullscreenDarkModeService;
  private fullscreenElement: Element | null = null;
  private isDarkMode: boolean = false;
  private observers: MutationObserver[] = [];

  private constructor() {
    this.initializeService();
  }

  public static getInstance(): FullscreenDarkModeService {
    if (!FullscreenDarkModeService.instance) {
      FullscreenDarkModeService.instance = new FullscreenDarkModeService();
    }
    return FullscreenDarkModeService.instance;
  }

  /**
   * تهيئة الخدمة
   */
  private initializeService(): void {
    this.detectDarkMode();
    this.setupEventListeners();
    this.setupMutationObservers();
    console.log('🖥️ تم تهيئة خدمة وضع ملء الشاشة والوضع المظلم');
  }

  /**
   * إعداد مستمعي الأحداث
   */
  private setupEventListeners(): void {
    // مراقبة تغييرات وضع ملء الشاشة
    document.addEventListener('fullscreenchange', () => {
      this.handleFullscreenChange();
    });

    // مراقبة تغييرات الوضع المظلم
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', () => {
      this.detectDarkMode();
      this.applyFullscreenFixes();
    });

    // مراقبة تغييرات class في document
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          this.detectDarkMode();
          this.applyFullscreenFixes();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    this.observers.push(observer);
  }

  /**
   * إعداد مراقبي التغييرات
   */
  private setupMutationObservers(): void {
    // مراقبة تغييرات data-theme
    const themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'data-theme' || mutation.attributeName === 'class')) {
          this.detectDarkMode();
          this.applyFullscreenFixes();
        }
      });
    });

    themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme', 'class']
    });

    themeObserver.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-theme', 'class']
    });

    this.observers.push(themeObserver);
  }

  /**
   * كشف الوضع المظلم
   */
  private detectDarkMode(): void {
    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    // فحص class="dark"
    const hasDarkClass = htmlElement.classList.contains('dark') || 
                        bodyElement.classList.contains('dark');

    // فحص data-theme="dark"
    const hasDarkTheme = htmlElement.getAttribute('data-theme') === 'dark' ||
                        bodyElement.getAttribute('data-theme') === 'dark';

    // فحص تفضيلات النظام
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    this.isDarkMode = hasDarkClass || hasDarkTheme || systemPrefersDark;
  }

  /**
   * معالجة تغيير وضع ملء الشاشة
   */
  private handleFullscreenChange(): void {
    this.fullscreenElement = document.fullscreenElement;
    
    if (this.fullscreenElement) {
      console.log('🖥️ دخول وضع ملء الشاشة');
      this.applyFullscreenFixes();
    } else {
      console.log('🖥️ الخروج من وضع ملء الشاشة');
      this.removeFullscreenFixes();
    }
  }

  /**
   * تطبيق إصلاحات وضع ملء الشاشة
   */
  private applyFullscreenFixes(): void {
    if (!this.fullscreenElement) return;

    // تطبيق class للوضع المظلم على العنصر في ملء الشاشة
    if (this.isDarkMode) {
      this.fullscreenElement.classList.add('dark');
      this.fullscreenElement.setAttribute('data-theme', 'dark');
    } else {
      this.fullscreenElement.classList.remove('dark');
      this.fullscreenElement.removeAttribute('data-theme');
    }

    // إضافة class مخصص لإصلاحات ملء الشاشة
    this.fullscreenElement.classList.add('fullscreen-dark-mode-fixed');

    // تطبيق أنماط CSS مباشرة كحل احتياطي
    this.applyInlineStyles();

    console.log(`🖥️ تم تطبيق إصلاحات ملء الشاشة - الوضع المظلم: ${this.isDarkMode}`);
  }

  /**
   * إزالة إصلاحات وضع ملء الشاشة
   */
  private removeFullscreenFixes(): void {
    // إزالة class مخصص
    document.querySelectorAll('.fullscreen-dark-mode-fixed').forEach(element => {
      element.classList.remove('fullscreen-dark-mode-fixed');
    });

    console.log('🖥️ تم إزالة إصلاحات ملء الشاشة');
  }

  /**
   * تطبيق أنماط CSS مباشرة كحل احتياطي
   */
  private applyInlineStyles(): void {
    if (!this.fullscreenElement) return;

    const element = this.fullscreenElement as HTMLElement;
    
    // تطبيق أنماط شريط التمرير
    const scrollbarStyles = this.isDarkMode ? {
      'scrollbar-color': 'rgba(75, 85, 99, 0.5) transparent',
      'scrollbar-width': 'thin'
    } : {
      'scrollbar-color': 'rgba(156, 163, 175, 0.5) transparent',
      'scrollbar-width': 'thin'
    };

    Object.assign(element.style, scrollbarStyles);

    // إضافة أنماط CSS ديناميكية
    this.injectDynamicStyles();
  }

  /**
   * حقن أنماط CSS ديناميكية
   */
  private injectDynamicStyles(): void {
    const existingStyle = document.getElementById('fullscreen-dynamic-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    const style = document.createElement('style');
    style.id = 'fullscreen-dynamic-styles';
    
    const scrollbarColor = this.isDarkMode ? 
      'rgba(75, 85, 99, 0.5)' : 'rgba(156, 163, 175, 0.5)';
    
    const scrollbarHoverColor = this.isDarkMode ? 
      'rgba(107, 114, 128, 0.7)' : 'rgba(107, 114, 128, 0.7)';

    style.textContent = `
      .fullscreen-dark-mode-fixed::-webkit-scrollbar {
        width: 10px !important;
        height: 10px !important;
        -webkit-appearance: none !important;
      }
      
      .fullscreen-dark-mode-fixed::-webkit-scrollbar-track {
        background: transparent !important;
        border: none !important;
      }
      
      .fullscreen-dark-mode-fixed::-webkit-scrollbar-thumb {
        background: transparent !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
        border: none !important;
        -webkit-appearance: none !important;
      }
      
      .fullscreen-dark-mode-fixed:hover::-webkit-scrollbar-thumb,
      .fullscreen-dark-mode-fixed:focus-within::-webkit-scrollbar-thumb {
        background: ${scrollbarColor} !important;
      }
      
      .fullscreen-dark-mode-fixed::-webkit-scrollbar-thumb:hover {
        background: ${scrollbarHoverColor} !important;
      }
      
      .fullscreen-dark-mode-fixed *::-webkit-scrollbar {
        width: 8px !important;
        height: 8px !important;
        -webkit-appearance: none !important;
      }
      
      .fullscreen-dark-mode-fixed *::-webkit-scrollbar-track {
        background: transparent !important;
        border: none !important;
      }
      
      .fullscreen-dark-mode-fixed *::-webkit-scrollbar-thumb {
        background: transparent !important;
        border-radius: 10px !important;
        transition: all 0.3s ease !important;
        border: none !important;
        -webkit-appearance: none !important;
      }
      
      .fullscreen-dark-mode-fixed *:hover::-webkit-scrollbar-thumb,
      .fullscreen-dark-mode-fixed *:focus-within::-webkit-scrollbar-thumb {
        background: ${scrollbarColor} !important;
      }
      
      .fullscreen-dark-mode-fixed *::-webkit-scrollbar-thumb:hover {
        background: ${scrollbarHoverColor} !important;
      }
      
      .fullscreen-dark-mode-fixed::-webkit-scrollbar-button,
      .fullscreen-dark-mode-fixed *::-webkit-scrollbar-button {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
        background: none !important;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        -webkit-appearance: none !important;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * فرض تحديث الأنماط
   */
  public forceUpdate(): void {
    this.detectDarkMode();
    if (this.fullscreenElement) {
      this.applyFullscreenFixes();
    }
  }

  /**
   * تنظيف الخدمة
   */
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    const dynamicStyles = document.getElementById('fullscreen-dynamic-styles');
    if (dynamicStyles) {
      dynamicStyles.remove();
    }
  }

  /**
   * الحصول على حالة الوضع المظلم
   */
  public getIsDarkMode(): boolean {
    return this.isDarkMode;
  }

  /**
   * الحصول على حالة ملء الشاشة
   */
  public getIsFullscreen(): boolean {
    return !!this.fullscreenElement;
  }
}

// تهيئة تلقائية للخدمة
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      FullscreenDarkModeService.getInstance();
    });
  } else {
    FullscreenDarkModeService.getInstance();
  }
}

export default FullscreenDarkModeService;
