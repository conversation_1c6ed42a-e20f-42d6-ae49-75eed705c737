/**
 * 🖥️ إصلاحات وضع ملء الشاشة - SmartPOS Fullscreen Fixes
 * 
 * حل شامل لمشاكل شريط التمرير في وضع ملء الشاشة
 * يضمن عمل الوضع المظلم بشكل صحيح في ملء الشاشة
 */

/* ========================================
   إصلاح شريط التمرير في وضع ملء الشاشة
   ======================================== */

/* تطبيق أنماط شريط التمرير على العنصر في ملء الشاشة */
:fullscreen {
  /* إعادة تعيين أنماط شريط التمرير */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* شريط التمرير الأساسي في ملء الشاشة */
:fullscreen::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  -webkit-appearance: none !important;
}

:fullscreen::-webkit-scrollbar-track {
  background: transparent;
  border: none !important;
}

:fullscreen::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: none !important;
  -webkit-appearance: none !important;
}

/* إظهار شريط التمرير عند التحويم في ملء الشاشة */
:fullscreen:hover::-webkit-scrollbar-thumb,
:fullscreen:focus-within::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6) !important;
}

:fullscreen::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* ========================================
   الوضع المظلم في ملء الشاشة
   ======================================== */

/* تطبيق الوضع المظلم على العنصر في ملء الشاشة */
:fullscreen.dark,
:fullscreen[data-theme="dark"] {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

/* شريط التمرير للوضع المظلم في ملء الشاشة */
:fullscreen.dark::-webkit-scrollbar-thumb,
:fullscreen[data-theme="dark"]::-webkit-scrollbar-thumb {
  background: transparent;
}

:fullscreen.dark:hover::-webkit-scrollbar-thumb,
:fullscreen.dark:focus-within::-webkit-scrollbar-thumb,
:fullscreen[data-theme="dark"]:hover::-webkit-scrollbar-thumb,
:fullscreen[data-theme="dark"]:focus-within::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6) !important;
}

:fullscreen.dark::-webkit-scrollbar-thumb:hover,
:fullscreen[data-theme="dark"]::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* ========================================
   إصلاح شامل للعناصر الفرعية في ملء الشاشة
   ======================================== */

/* تطبيق الأنماط على جميع العناصر داخل ملء الشاشة */
:fullscreen * {
  /* إعادة تعيين أنماط شريط التمرير للعناصر الفرعية */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* شريط التمرير للعناصر الفرعية في ملء الشاشة */
:fullscreen *::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  -webkit-appearance: none;
}

:fullscreen *::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
  border: none;
}

:fullscreen *::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 10px;
  transition: all 0.3s ease;
  border: none;
  -webkit-appearance: none;
}

/* إظهار شريط التمرير للعناصر الفرعية عند التحويم */
:fullscreen *:hover::-webkit-scrollbar-thumb,
:fullscreen *:focus-within::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5) !important;
}

:fullscreen *::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* ========================================
   الوضع المظلم للعناصر الفرعية في ملء الشاشة
   ======================================== */

/* تطبيق الوضع المظلم على العناصر الفرعية */
:fullscreen.dark *,
:fullscreen[data-theme="dark"] * {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

/* شريط التمرير للوضع المظلم للعناصر الفرعية */
:fullscreen.dark *::-webkit-scrollbar-thumb,
:fullscreen[data-theme="dark"] *::-webkit-scrollbar-thumb {
  background: transparent;
}

:fullscreen.dark *:hover::-webkit-scrollbar-thumb,
:fullscreen.dark *:focus-within::-webkit-scrollbar-thumb,
:fullscreen[data-theme="dark"] *:hover::-webkit-scrollbar-thumb,
:fullscreen[data-theme="dark"] *:focus-within::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5) !important;
}

:fullscreen.dark *::-webkit-scrollbar-thumb:hover,
:fullscreen[data-theme="dark"] *::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* ========================================
   إزالة أسهم شريط التمرير في ملء الشاشة
   ======================================== */

/* إزالة جميع أسهم شريط التمرير في ملء الشاشة */
:fullscreen::-webkit-scrollbar-button,
:fullscreen *::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-appearance: none !important;
}

/* إزالة زوايا شريط التمرير في ملء الشاشة */
:fullscreen::-webkit-scrollbar-corner,
:fullscreen *::-webkit-scrollbar-corner {
  background: transparent !important;
  display: none !important;
}

/* ========================================
   دعم Firefox في ملء الشاشة
   ======================================== */

@-moz-document url-prefix() {
  :fullscreen,
  :fullscreen * {
    scrollbar-width: thin;
    scrollbar-arrow-color: transparent;
  }
  
  :fullscreen.dark,
  :fullscreen.dark *,
  :fullscreen[data-theme="dark"],
  :fullscreen[data-theme="dark"] * {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
  }
}

/* ========================================
   إعدادات إضافية للتوافق
   ======================================== */

/* ضمان عدم تداخل الأنماط */
:fullscreen {
  /* إعادة تعيين أي أنماط قد تتداخل */
  overflow: auto;
}

/* تحسين الأداء في ملء الشاشة */
:fullscreen * {
  /* تحسين الرسم */
  will-change: scroll-position;
}

/* دعم الشاشات عالية الدقة في ملء الشاشة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  :fullscreen::-webkit-scrollbar,
  :fullscreen *::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

/* دعم إعدادات الحركة المخفضة في ملء الشاشة */
@media (prefers-reduced-motion: reduce) {
  :fullscreen::-webkit-scrollbar-thumb,
  :fullscreen *::-webkit-scrollbar-thumb {
    transition: none !important;
  }
}

/* دعم التباين العالي في ملء الشاشة */
@media (prefers-contrast: high) {
  :fullscreen:hover::-webkit-scrollbar-thumb,
  :fullscreen *:hover::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.8) !important;
  }
  
  :fullscreen.dark:hover::-webkit-scrollbar-thumb,
  :fullscreen.dark *:hover::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.8) !important;
  }
}

/* ========================================
   تأكيد نهائي - ضمان العمل في جميع الحالات
   ======================================== */

/* تطبيق قوي للأنماط */
:fullscreen,
:fullscreen *,
:fullscreen.dark,
:fullscreen.dark *,
:fullscreen[data-theme="dark"],
:fullscreen[data-theme="dark"] * {
  /* ضمان عدم وجود أسهم */
  -webkit-scrollbar-button: none !important;
}

/* تأكيد إزالة الأسهم */
:fullscreen::-webkit-scrollbar-button:start:decrement,
:fullscreen::-webkit-scrollbar-button:end:increment,
:fullscreen *::-webkit-scrollbar-button:start:decrement,
:fullscreen *::-webkit-scrollbar-button:end:increment {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
}
