/*
 * 📜 نظام شريط التمرير المخصص - SmartPOS Custom Scrollbar System
 * 🎨 شريط تمرير هادئ وعائم يظهر فقط عند التمرير
 * 🚫 بدون أسهم التمرير التقليدية
 * 🌙 دعم كامل للوضع المظلم والفاتح
 */

/* ========================================
   إعدادات عامة - إزالة أسهم التمرير من كل التطبيق
   ======================================== */

/* إزالة أسهم التمرير من جميع العناصر - بجميع الاتجاهات */
*::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
}

/* إزالة أسهم التمرير العمودية */
*::-webkit-scrollbar-button:vertical {
  display: none !important;
  height: 0 !important;
}

/* إزالة أسهم التمرير الأفقية */
*::-webkit-scrollbar-button:horizontal {
  display: none !important;
  width: 0 !important;
}

/* إزالة أسهم التمرير في جميع الاتجاهات */
*::-webkit-scrollbar-button:start:decrement,
*::-webkit-scrollbar-button:end:increment {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
}

/* إزالة زوايا شريط التمرير */
*::-webkit-scrollbar-corner {
  background: transparent !important;
  display: none !important;
}

/* إزالة أي مساحة إضافية للأسهم */
*::-webkit-scrollbar-track-piece {
  background: transparent !important;
}

/* ========================================
   شريط التمرير العام للتطبيق - يطبق على كل العناصر
   ======================================== */

/* تطبيق على جميع العناصر القابلة للتمرير */
* {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* شريط التمرير للعناصر العامة */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  /* التأكد من عدم وجود أسهم */
  -webkit-appearance: none;
}

*::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
  /* إزالة أي حدود أو أسهم */
  border: none;
}

*::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 10px;
  transition: all 0.3s ease;
  /* إزالة أي حدود */
  border: none;
  /* التأكد من عدم وجود أسهم */
  -webkit-appearance: none;
}

/* إظهار الشريط عند التحويم أو التمرير */
*:hover::-webkit-scrollbar-thumb,
*:focus-within::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* الوضع المظلم */
.dark *:hover::-webkit-scrollbar-thumb,
.dark *:focus-within::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark *::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* Firefox */
.dark * {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

*:hover,
*:focus-within {
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* ========================================
   إعدادات إضافية لضمان إزالة الأسهم
   ======================================== */

/* إزالة الأسهم من جميع عناصر HTML */
html::-webkit-scrollbar-button,
body::-webkit-scrollbar-button,
div::-webkit-scrollbar-button,
section::-webkit-scrollbar-button,
article::-webkit-scrollbar-button,
aside::-webkit-scrollbar-button,
nav::-webkit-scrollbar-button,
main::-webkit-scrollbar-button,
header::-webkit-scrollbar-button,
footer::-webkit-scrollbar-button,
ul::-webkit-scrollbar-button,
ol::-webkit-scrollbar-button,
li::-webkit-scrollbar-button,
table::-webkit-scrollbar-button,
tbody::-webkit-scrollbar-button,
thead::-webkit-scrollbar-button,
tr::-webkit-scrollbar-button,
td::-webkit-scrollbar-button,
th::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
  border: none !important;
  -webkit-appearance: none !important;
}

/* ========================================
   شريط التمرير الرئيسي للصفحة
   ======================================== */

/* شريط التمرير الرئيسي للـ html و body */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  -webkit-appearance: none !important;
}

html::-webkit-scrollbar-track,
body::-webkit-scrollbar-track {
  background: transparent;
  border: none !important;
}

html::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: none !important;
  -webkit-appearance: none !important;
}

html:hover::-webkit-scrollbar-thumb,
body:hover::-webkit-scrollbar-thumb,
html:focus-within::-webkit-scrollbar-thumb,
body:focus-within::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
}

html::-webkit-scrollbar-thumb:hover,
body::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* الوضع المظلم للشريط الرئيسي */
.dark html:hover::-webkit-scrollbar-thumb,
.dark body:hover::-webkit-scrollbar-thumb,
.dark html:focus-within::-webkit-scrollbar-thumb,
.dark body:focus-within::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6);
}

.dark html::-webkit-scrollbar-thumb:hover,
.dark body::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* ========================================
   فئات مخصصة للحالات الخاصة
   ======================================== */

/* شريط رفيع للمساحات الضيقة */
.custom-scrollbar-thin::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  -webkit-appearance: none !important;
}

.custom-scrollbar-thin::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* شريط بألوان أساسية */
.custom-scrollbar-primary:hover::-webkit-scrollbar-thumb,
.custom-scrollbar-primary:focus-within::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.6) !important;
}

.dark .custom-scrollbar-primary:hover::-webkit-scrollbar-thumb,
.dark .custom-scrollbar-primary:focus-within::-webkit-scrollbar-thumb {
  background: rgba(96, 165, 250, 0.6) !important;
}

.custom-scrollbar-primary::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* ========================================
   شريط التمرير المخفي - لإخفاء الشريط تماماً
   ======================================== */

.scrollbar-hide {
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* ========================================
   إعدادات إضافية لضمان إزالة الأسهم في جميع الحالات
   ======================================== */

/* إزالة الأسهم من جميع الحالات الممكنة */
::-webkit-scrollbar-button:single-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

::-webkit-scrollbar-button:double-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* إزالة الأسهم من الاتجاهات المختلفة */
::-webkit-scrollbar-button:vertical:start:increment,
::-webkit-scrollbar-button:vertical:end:decrement,
::-webkit-scrollbar-button:horizontal:start:increment,
::-webkit-scrollbar-button:horizontal:end:decrement {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
}

/* إزالة أي خلفية أو حدود للأسهم */
::-webkit-scrollbar-button {
  background-image: none !important;
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* ========================================
   دعم إعدادات الحركة المخفضة
   ======================================== */

@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }

  *::-webkit-scrollbar-thumb {
    transition: none !important;
  }

  /* التأكد من إزالة الأسهم حتى مع الحركة المخفضة */
  *::-webkit-scrollbar-button {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }
}

/* ========================================
   توثيق الاستخدام
   ======================================== */

/*
 * 🎯 نظام شريط التمرير المخصص:
 *
 * ✅ يطبق على جميع العناصر تلقائياً
 * ✅ عائم - يظهر فقط عند التحويم أو التمرير
 * ✅ بدون أسهم - تصميم نظيف
 * ✅ ألوان هادئة - رمادي فاتح
 * ✅ دعم الوضع المظلم والفاتح
 * ✅ انتقالات سلسة
 * ✅ متوافق مع جميع المتصفحات
 *
 * الفئات المتاحة:
 * - .custom-scrollbar-thin - شريط رفيع (6px)
 * - .custom-scrollbar-primary - شريط بألوان أساسية
 * - .scrollbar-hide - إخفاء الشريط تماماً
 */
